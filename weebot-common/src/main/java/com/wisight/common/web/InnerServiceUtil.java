package com.wisight.common.web;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wisight.common.Constant;
import com.wisight.common.core.ResponseResult;
import com.wisight.common.tenant.SudoContext;
import com.wisight.common.tenant.TenantSession;
import com.wisight.common.util.JSON;
import feign.*;
import feign.codec.ErrorDecoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.slf4j.Slf4jLogger;
import io.seata.core.context.RootContext;
import com.yomahub.tlog.context.TLogContext;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * InnerServiceUtil
 *
 * <AUTHOR>
 * @date 2022/8/17 16:32
 */
public class InnerServiceUtil {

    private static final ObjectMapper OM = JSON.newObjectMapper();
    private static final ThreadLocal<HttpServletRequest> REQUEST = new TransmittableThreadLocal<>();

    public static <T> T createService(Class<T> apiType, String url) {
        return Feign.builder()
                .requestInterceptor(new SvcInterceptor())
                .encoder(new JacksonEncoder(OM))
                .queryMapEncoder(new MyQueryMapEncoder())
                .mapAndDecode(new ResponseMapper(), new JacksonDecoder(OM))
                .errorDecoder(new CustomErrorDecoder())
                .logger(new Slf4jLogger())
//                .logLevel(Logger.Level.FULL)
                .target(apiType, url);
    }

    public static void setInnerCallRequest(HttpServletRequest request) {
        REQUEST.set(request);
    }

    public static void clearInnerCallRequest() {
        REQUEST.remove();
    }

    public static boolean isServiceCall() {
        HttpServletRequest request = REQUEST.get();
        if (request == null) {
            return false;
        }
        String isServiceCall = request.getHeader(Constant.HttpHeaders.X_SERVICE_CALL);
        return "true".equals(isServiceCall);
    }

    public static boolean isSudoServiceCall() {
        HttpServletRequest request = REQUEST.get();
        if (request == null) {
            return false;
        }

        String isSudo = request.getHeader(Constant.HttpHeaders.SUDO);
        return isServiceCall() && "true".equals(isSudo);
    }

    /**
     * 获取内部调用所需的请求头
     *
     * @return
     */
    public static Map<String, String> getInnerCallRequestHeader() {
        Map<String, String> headers = new HashMap<>();
        String applicationName = SpringUtil.getApplicationName();
        applicationName = StrUtil.isBlank(applicationName) ? "unnamed-app" : applicationName;
        headers.put(Constant.HttpHeaders.X_SERVICE_NAME, applicationName);
        headers.put(Constant.HttpHeaders.TRACE_ID, TLogContext.getTraceId());
        headers.put(RootContext.KEY_XID, RootContext.getXID());
        headers.put(Constant.HttpHeaders.X_SERVICE_CALL, "true");
        if (SudoContext.isSudo()) {
            headers.put(Constant.HttpHeaders.SUDO, "true");
        }

        TenantSession session = SessionContext.getSession();
        if (session != null) {
            headers.put(Constant.HttpHeaders.X_COMPANY_ID, session.getCompanyId() + "");
            // 服务与服务之间调用只传递TenantSession的信息
            TenantSession sessionToken = new TenantSession();
            sessionToken.setPrincipalId(session.getPrincipalId());
            sessionToken.setType(session.getType());
            sessionToken.setToken(session.getToken());
            sessionToken.setExpireTime(session.getExpireTime());
            sessionToken.setPrincipalName(session.getPrincipalName());
            sessionToken.setPrincipalId(session.getPrincipalId());
            sessionToken.setCompanyId(session.getCompanyId());
            sessionToken.setCompanyName(session.getCompanyName());
            headers.put(HttpHeaders.AUTHORIZATION, "Bearer " + Base64.encode(JSON.toJSON(sessionToken)));
        }
        return headers;
    }

    private static class SvcInterceptor implements RequestInterceptor {
        @Override
        public void apply(RequestTemplate template) {
            getInnerCallRequestHeader().forEach(template::header);
        }
    }

    private static class ResponseMapper implements feign.ResponseMapper {

        @Override
        public Response map(Response response, Type type) {
            try {
                String body = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
                ResponseResult result = JSON.fromJSON(body, ResponseResult.class);
                if (result.getRetcode() != 0) {
                    throw new RuntimeException(StrUtil.format("内部服务调用异常！code={}，message={}", result.getRetcode(), result.getMsg()));
                }
                return response.toBuilder().body(JSON.toJSON(result.getResult()), StandardCharsets.UTF_8).build();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private static String getServiceName(Response response) {
        String url = response.request().url();
        String serviceName;
        if (url.contains(":12350")) {
            serviceName = "basedata";
        } else if (url.contains(":12347")) {
            serviceName = "brain";
        } else if (url.contains(":12355")) {
            serviceName = "external";
        } else if (url.contains(":12352")) {
            serviceName = "sdk";
        } else if (url.contains(":12349")) {
            serviceName = "vRobots";
        } else if (url.contains(":12351")) {
            serviceName = "web";
        } else {
            serviceName = "unknown";
        }
        return serviceName;
    }

    public static class MyQueryMapEncoder implements QueryMapEncoder {

        @Override
        public Map<String, Object> encode(Object object) {
            return JSON.convert(object, Map.class);
        }
    }

    private static class CustomErrorDecoder implements ErrorDecoder {

        private final ErrorDecoder errorDecoder = new Default();

        @Override
        public Exception decode(String methodKey, Response response) {
            Exception exception;
            if (HttpStatus.valueOf(response.status()).is5xxServerError()) {
                String serviceName = getServiceName(response);
                try {
                    // 获取原始的返回内容
                    if (response.body() == null) {
                        exception = new BizException(ResultStatus.Common.ERROR_INTERNAL_SERVICE_CALL_FAILED, serviceName + ": 接口调用失败，服务不在线！");
                    } else {
                        String json = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
                        ResponseResult result = JSON.fromJSON(json, ResponseResult.class);
                        exception = new BizException(result, serviceName);
                    }
                } catch (IOException ex) {
                    exception = new BizException(ResultStatus.Common.ERROR_INTERNAL_SERVICE_CALL_FAILED, serviceName + ": 接口调用失败，数据解析异常，错误信息为：" + ex.getMessage());
                }
            } else {
                exception = errorDecoder.decode(methodKey, response);
            }
            return exception;
        }
    }
}
