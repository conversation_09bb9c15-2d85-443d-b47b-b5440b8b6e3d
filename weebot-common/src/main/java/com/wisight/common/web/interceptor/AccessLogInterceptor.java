package com.wisight.common.web.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.wisight.common.Constant;
import com.wisight.common.util.JSON;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.http.HttpHeaders;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.util.Enumeration;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class AccessLogInterceptor implements HandlerInterceptor {
    private static final ThreadLocal<Long> START_TIME = new ThreadLocal<>();
    private static final ThreadLocal<String> USER_AGENT = new ThreadLocal<>();
    private final static AtomicInteger MAX_CONNECTIONS = new AtomicInteger(0);

    private static Boolean enableLog = true;

    private static void logDS() {
        if (!enableLog) {
            return;
        }
        try {
            DataSource realDataSource = SpringUtil.getBean(DataSource.class);
            // 获取HikariDataSource'
            if (realDataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) realDataSource;
                // 获取当前活跃的连接数
                HikariPoolMXBean hikariPoolMXBean = hikariDataSource.getHikariPoolMXBean();
                int activeConnections = hikariPoolMXBean.getActiveConnections();

                // 获取当前空闲的连接数
                int idleConnections = hikariPoolMXBean.getIdleConnections();

                // 获取当前总数的连接数
                int totalConnections = hikariPoolMXBean.getTotalConnections();

                int threadsAwaitingConnection = hikariPoolMXBean.getThreadsAwaitingConnection();

                MAX_CONNECTIONS.set(Math.max(MAX_CONNECTIONS.get(), activeConnections));
                log.debug(
                        " 当前活跃的连接数：{}  当前空闲的连接数：{}  当前总数的连接数：{}  当前等待获取连接的线程：{}  最大连接数：{}",
                        activeConnections, idleConnections, totalConnections, threadsAwaitingConnection,
                        MAX_CONNECTIONS.get());
            }

        } catch (Exception e) {
            log.warn("获取数据源失败", e);
            if (e instanceof NoSuchBeanDefinitionException) {
                enableLog = false;
            }
        }
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        START_TIME.set(System.currentTimeMillis());

        String userAgent = request.getHeader(HttpHeaders.USER_AGENT);
        String caller = request.getHeader(Constant.HttpHeaders.X_SERVICE_NAME);

        if (StrUtil.isNotBlank(caller)) {
            // 外部请求，设置调用方为客户端信息
            userAgent += " " + caller;
        }

        // 设置本次请求的一些信息
        USER_AGENT.set(userAgent);
        logDS();
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        long beginTime = START_TIME.get();
        long endTime = System.currentTimeMillis();

        String header = "";
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            header += headerName + "=" + headerValue + ", ";
        }

        Map<String, String[]> pm = request.getParameterMap();
        log.debug("耗时：{}ms\tURI：{} {}\t请求参数：{}\ttoken：{}\t最大内存：{}m\t已分配内存：{}m\t已分配内存中的剩余空间：{}m\t最大可用内存：{}m\tuser-agent：【{}】 \theader: {}",
                (endTime - beginTime), request.getMethod(), request.getRequestURI(), JSON.toJSON(pm), request.getHeader(HttpHeaders.AUTHORIZATION), Runtime.getRuntime().maxMemory() / 1024 / 1024, Runtime.getRuntime().totalMemory() / 1024 / 1024, Runtime.getRuntime().freeMemory() / 1024 / 1024,
                (Runtime.getRuntime().maxMemory() - Runtime.getRuntime().totalMemory() + Runtime.getRuntime().freeMemory()) / 1024 / 1024, USER_AGENT.get(), header);
        logDS();
        START_TIME.remove();
        USER_AGENT.remove();
    }
}
