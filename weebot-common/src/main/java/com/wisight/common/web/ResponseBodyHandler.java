package com.wisight.common.web;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wisight.common.Constant;
import com.wisight.common.core.ResponseResult;
import com.wisight.common.core.dto.PageResp;
import com.wisight.common.core.util.R;
import com.wisight.common.util.JSON;
import com.yomahub.tlog.context.TLogContext;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@RestControllerAdvice
public class ResponseBodyHandler implements ResponseBodyAdvice<Object> {

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        if (ObjectUtil.isEmpty(body) && returnType.getParameterType() == String.class) {
            response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        } else if (IPage.class.isAssignableFrom(returnType.getParameterType())) {
            body = PageResp.of((IPage) body);
        } else if (R.class.isAssignableFrom(returnType.getParameterType())) {
            R r = (R) body;
            ResponseResult result = new ResponseResult<>();
            ResultStatus.Common status = ResultStatus.Common.codeOf(r.getCode());
            result.setResult(r.getData());
            result.setRetcode(r.getCode());
            result.setTraceId(TLogContext.getTraceId());
            if (status == ResultStatus.Common.SUCCESS) {
                result.setMsg(new BizException(status, r.getMsg()).getMessage());
            } else {
                result.setMsg(r.getMsg());
            }
            return result;
        }

        ResponseResult<Object> result = new ResponseResult<>(body, ResultStatus.Common.SUCCESS.getRetcode(), null, null, TLogContext.getTraceId());
        if (body instanceof String) {
            // 对字符串进行特殊处理，不然会报类型转化异常
            response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            return JSON.toJSON(result);
        }else {
            return result;
        }
    }

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        if (ResponseResult.class.isAssignableFrom(returnType.getParameterType())
                || ResponseEntity.class.isAssignableFrom(returnType.getParameterType())
                || returnType.hasMethodAnnotation(NotWrapResponse.class)) {
            return false;
        }
        return true;
    }

}
